const Footer = () => {
  const currentYear = new Date().getFullYear();

  return (
    <footer className="glass-dark mt-20 border-t border-white/10">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {/* Logo and Description */}
          <div className="text-center md:text-right">
            <div className="flex items-center justify-center md:justify-start space-x-3 rtl:space-x-reverse mb-4">
              <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-lg">ذ</span>
              </div>
              <h3 className="text-xl font-bold text-white">الذكر الحكيم</h3>
            </div>
            <p className="text-gray-300 text-sm leading-relaxed">
              موقع إسلامي شامل يحتوي على القرآن الكريم والأذكار والتسبيح
              <br />
              لتقريب المسلم من ربه وتذكيره بذكر الله تعالى
            </p>
          </div>

          {/* Quick Links */}
          <div className="text-center">
            <h4 className="text-lg font-semibold text-white mb-4">روابط سريعة</h4>
            <ul className="space-y-2">
              <li>
                <a href="/quran" className="text-gray-300 hover:text-blue-400 transition-colors duration-300">
                  📖 القرآن الكريم
                </a>
              </li>
              <li>
                <a href="/azkar" className="text-gray-300 hover:text-blue-400 transition-colors duration-300">
                  🤲 الأذكار
                </a>
              </li>
              <li>
                <a href="/tasbeeh" className="text-gray-300 hover:text-blue-400 transition-colors duration-300">
                  📿 التسبيح
                </a>
              </li>
              <li>
                <a href="/about" className="text-gray-300 hover:text-blue-400 transition-colors duration-300">
                  ℹ️ حولنا
                </a>
              </li>
            </ul>
          </div>

          {/* Contact and Social */}
          <div className="text-center md:text-left">
            <h4 className="text-lg font-semibold text-white mb-4">تواصل معنا</h4>
            <div className="space-y-3">
              <div className="flex items-center justify-center md:justify-start space-x-3 rtl:space-x-reverse">
                <span className="text-blue-400">📧</span>
                <span className="text-gray-300 text-sm"><EMAIL></span>
              </div>
              <div className="flex items-center justify-center md:justify-start space-x-3 rtl:space-x-reverse">
                <span className="text-blue-400">🌐</span>
                <span className="text-gray-300 text-sm">www.alzekr-alhakeem.com</span>
              </div>
            </div>

            {/* Social Media Icons */}
            <div className="flex justify-center md:justify-start space-x-4 rtl:space-x-reverse mt-6">
              <a href="#" className="text-gray-400 hover:text-blue-400 transition-colors duration-300">
                <span className="text-2xl">📘</span>
              </a>
              <a href="#" className="text-gray-400 hover:text-blue-400 transition-colors duration-300">
                <span className="text-2xl">📱</span>
              </a>
              <a href="#" className="text-gray-400 hover:text-blue-400 transition-colors duration-300">
                <span className="text-2xl">📺</span>
              </a>
            </div>
          </div>
        </div>

        {/* Bottom Section */}
        <div className="border-t border-white/10 mt-8 pt-8">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <div className="text-center md:text-right mb-4 md:mb-0">
              <p className="text-gray-400 text-sm">
                © {currentYear} الذكر الحكيم. جميع الحقوق محفوظة.
              </p>
            </div>
            <div className="text-center md:text-left">
              <p className="text-gray-400 text-sm">
                تم التطوير بـ ❤️ لخدمة الإسلام والمسلمين
              </p>
            </div>
          </div>
        </div>

        {/* Islamic Quote */}
        <div className="text-center mt-8 p-4 glass rounded-lg">
          <p className="text-gold-400 font-arabic text-lg mb-2">
            "وَاذْكُرُوا اللَّهَ كَثِيرًا لَّعَلَّكُمْ تُفْلِحُونَ"
          </p>
          <p className="text-gray-300 text-sm">
            سورة الأنفال - آية 45
          </p>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
