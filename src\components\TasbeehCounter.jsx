import { useState, useEffect } from 'react';

const TasbeehCounter = ({ tasbeeh, onComplete }) => {
  const [count, setCount] = useState(0);
  const [target, setTarget] = useState(tasbeeh?.recommendedCount || 33);
  const [isCompleted, setIsCompleted] = useState(false);
  const [showCelebration, setShowCelebration] = useState(false);

  // Load saved count from localStorage
  useEffect(() => {
    const savedCount = localStorage.getItem(`tasbeeh_${tasbeeh?.id}_count`);
    const savedTarget = localStorage.getItem(`tasbeeh_${tasbeeh?.id}_target`);
    
    if (savedCount) {
      setCount(parseInt(savedCount));
    }
    if (savedTarget) {
      setTarget(parseInt(savedTarget));
    }
  }, [tasbeeh?.id]);

  // Save count to localStorage
  useEffect(() => {
    if (tasbeeh?.id) {
      localStorage.setItem(`tasbeeh_${tasbeeh.id}_count`, count.toString());
      localStorage.setItem(`tasbeeh_${tasbeeh.id}_target`, target.toString());
    }
  }, [count, target, tasbeeh?.id]);

  // Check if target is reached
  useEffect(() => {
    if (count >= target && count > 0) {
      setIsCompleted(true);
      setShowCelebration(true);
      
      // Vibration feedback (if supported)
      if (navigator.vibrate) {
        navigator.vibrate([200, 100, 200]);
      }
      
      // Auto-hide celebration after 3 seconds
      setTimeout(() => {
        setShowCelebration(false);
      }, 3000);
      
      // Call completion callback
      if (onComplete) {
        onComplete(count, target);
      }
    } else {
      setIsCompleted(false);
    }
  }, [count, target, onComplete]);

  const increment = () => {
    setCount(prev => prev + 1);
    
    // Haptic feedback for mobile
    if (navigator.vibrate) {
      navigator.vibrate(50);
    }
  };

  const reset = () => {
    setCount(0);
    setIsCompleted(false);
    setShowCelebration(false);
  };

  const updateTarget = (newTarget) => {
    setTarget(Math.max(1, newTarget));
  };

  const progress = Math.min((count / target) * 100, 100);

  return (
    <div className="relative">
      {/* Celebration Animation */}
      {showCelebration && (
        <div className="absolute inset-0 flex items-center justify-center z-10 pointer-events-none">
          <div className="bg-green-500 text-white px-6 py-3 rounded-lg shadow-lg animate-bounce">
            <div className="text-center">
              <div className="text-2xl mb-2">🎉</div>
              <div className="font-bold">مبروك!</div>
              <div className="text-sm">تم إكمال الهدف</div>
            </div>
          </div>
        </div>
      )}

      <div className="glass-dark rounded-xl p-8 text-center">
        {/* Tasbeeh Text */}
        {tasbeeh && (
          <div className="mb-8">
            <h3 className="text-2xl font-bold text-white mb-2 arabic-text">
              {tasbeeh.text}
            </h3>
            <p className="text-gray-300 text-lg mb-2">
              {tasbeeh.translation}
            </p>
            <p className="text-blue-400 text-sm">
              {tasbeeh.englishText}
            </p>
          </div>
        )}

        {/* Progress Circle */}
        <div className="relative w-48 h-48 mx-auto mb-8">
          <svg className="w-full h-full transform -rotate-90" viewBox="0 0 100 100">
            {/* Background Circle */}
            <circle
              cx="50"
              cy="50"
              r="45"
              stroke="rgba(255,255,255,0.1)"
              strokeWidth="8"
              fill="none"
            />
            {/* Progress Circle */}
            <circle
              cx="50"
              cy="50"
              r="45"
              stroke={isCompleted ? "#10B981" : "#3B82F6"}
              strokeWidth="8"
              fill="none"
              strokeLinecap="round"
              strokeDasharray={`${2 * Math.PI * 45}`}
              strokeDashoffset={`${2 * Math.PI * 45 * (1 - progress / 100)}`}
              className="transition-all duration-300 ease-out"
            />
          </svg>
          
          {/* Counter Display */}
          <div className="absolute inset-0 flex items-center justify-center">
            <div className="text-center">
              <div className={`text-4xl font-bold ${isCompleted ? 'text-green-400' : 'text-white'}`}>
                {count}
              </div>
              <div className="text-gray-400 text-sm">
                من {target}
              </div>
              <div className="text-blue-400 text-xs mt-1">
                {progress.toFixed(0)}%
              </div>
            </div>
          </div>
        </div>

        {/* Target Selector */}
        <div className="mb-6">
          <label className="block text-gray-300 text-sm mb-2">الهدف المطلوب</label>
          <div className="flex items-center justify-center space-x-4 rtl:space-x-reverse">
            <button
              onClick={() => updateTarget(target - 1)}
              className="w-8 h-8 bg-red-500 hover:bg-red-600 rounded-full flex items-center justify-center text-white transition-colors duration-300"
              disabled={target <= 1}
            >
              -
            </button>
            <span className="text-white font-bold text-lg min-w-[3rem] text-center">
              {target}
            </span>
            <button
              onClick={() => updateTarget(target + 1)}
              className="w-8 h-8 bg-green-500 hover:bg-green-600 rounded-full flex items-center justify-center text-white transition-colors duration-300"
            >
              +
            </button>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="space-y-4">
          {/* Main Counter Button */}
          <button
            onClick={increment}
            className={`w-full py-4 px-8 rounded-xl font-bold text-lg transition-all duration-300 ${
              isCompleted
                ? 'bg-green-500 hover:bg-green-600 text-white'
                : 'bg-blue-500 hover:bg-blue-600 text-white'
            } btn-hover`}
          >
            {isCompleted ? '✅ مكتمل - اضغط للمتابعة' : '🤲 سبح الله'}
          </button>

          {/* Reset Button */}
          <button
            onClick={reset}
            className="w-full py-2 px-4 bg-gray-600 hover:bg-gray-700 text-white rounded-lg transition-colors duration-300"
          >
            🔄 إعادة تعيين
          </button>
        </div>

        {/* Reward Information */}
        {tasbeeh?.reward && (
          <div className="mt-6 p-4 bg-yellow-500/10 border border-yellow-500/20 rounded-lg">
            <h4 className="text-yellow-400 font-bold text-sm mb-2">الثواب:</h4>
            <p className="text-gray-300 text-sm arabic-text leading-relaxed">
              {tasbeeh.reward}
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

export default TasbeehCounter;
