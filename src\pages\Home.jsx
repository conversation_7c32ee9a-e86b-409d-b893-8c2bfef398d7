import { useEffect, useState } from 'react';
import { Link } from 'react-router-dom';
import Card, { StatsCard, ServiceCard } from '../components/Card';
import AOS from 'aos';

const Home = () => {
  const [quranData, setQuranData] = useState(null);
  const [azkarData, setAzkarData] = useState(null);

  useEffect(() => {
    AOS.init({
      duration: 1000,
      once: true,
    });

    // Load Quran data
    fetch('/data/quran.json')
      .then(response => response.json())
      .then(data => setQuranData(data))
      .catch(error => console.error('Error loading Quran data:', error));

    // Load Azkar data
    fetch('/data/azkar.json')
      .then(response => response.json())
      .then(data => setAzkarData(data))
      .catch(error => console.error('Error loading Azkar data:', error));
  }, []);

  const services = [
    {
      name: 'القرآن الكريم',
      description: 'اقرأ وتدبر آيات القرآن الكريم',
      icon: '📖',
      link: '/quran',
      gradient: 'from-green-500 to-teal-600'
    },
    {
      name: 'الأذكار',
      description: 'أذكار الصباح والمساء وأذكار متنوعة',
      icon: '🤲',
      link: '/azkar',
      gradient: 'from-purple-500 to-pink-600'
    },
    {
      name: 'التسبيح',
      description: 'مسبحة إلكترونية لعد التسبيحات',
      icon: '📿',
      link: '/tasbeeh',
      gradient: 'from-blue-500 to-indigo-600'
    }
  ];

  const quranStats = quranData ? [
    { value: quranData.info.totalSurahs, label: 'سورة' },
    { value: quranData.info.totalAyahs, label: 'آية' },
    { value: quranData.info.totalJuz, label: 'جزء' },
    { value: quranData.info.totalPages, label: 'صفحة' }
  ] : [];

  const azkarStats = azkarData ? [
    { value: azkarData.categories.length, label: 'فئة' },
    { value: azkarData.categories.reduce((total, cat) => total + cat.azkar.length, 0), label: 'ذكر' }
  ] : [];

  return (
    <div className="min-h-screen pt-20 pb-10">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        
        {/* Hero Section */}
        <section className="text-center mb-16" data-aos="fade-up">
          <div className="glass-dark rounded-2xl p-12 islamic-pattern">
            <h1 className="text-5xl md:text-6xl font-bold text-white mb-6 arabic-text">
              الذكر الحكيم
            </h1>
            <p className="text-xl md:text-2xl text-gray-300 mb-4 arabic-text">
              وَاذْكُرُوا اللَّهَ كَثِيرًا لَّعَلَّكُمْ تُفْلِحُونَ
            </p>
            <p className="text-lg text-blue-400 mb-8">
              موقع إسلامي شامل للقرآن الكريم والأذكار والتسبيح
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link
                to="/quran"
                className="bg-gradient-to-r from-green-500 to-teal-600 text-white px-8 py-3 rounded-lg font-bold hover:shadow-lg transition-all duration-300 btn-hover"
              >
                📖 ابدأ بقراءة القرآن
              </Link>
              <Link
                to="/azkar"
                className="bg-gradient-to-r from-purple-500 to-pink-600 text-white px-8 py-3 rounded-lg font-bold hover:shadow-lg transition-all duration-300 btn-hover"
              >
                🤲 تصفح الأذكار
              </Link>
            </div>
          </div>
        </section>

        {/* Services Section */}
        <section className="mb-16">
          <h2 className="text-3xl font-bold text-white text-center mb-8" data-aos="fade-up">
            خدماتنا
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {services.map((service, index) => (
              <div key={service.name} data-aos="fade-up" data-aos-delay={index * 200}>
                <ServiceCard service={service} />
              </div>
            ))}
          </div>
        </section>

        {/* Statistics Section */}
        <section className="mb-16">
          <h2 className="text-3xl font-bold text-white text-center mb-8" data-aos="fade-up">
            إحصائيات الموقع
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <div data-aos="fade-right">
              <StatsCard
                title="القرآن الكريم"
                icon="📖"
                stats={quranStats}
                gradient="from-green-500 to-teal-600"
              />
            </div>
            <div data-aos="fade-left">
              <StatsCard
                title="الأذكار"
                icon="🤲"
                stats={azkarStats}
                gradient="from-purple-500 to-pink-600"
              />
            </div>
          </div>
        </section>

        {/* Features Section */}
        <section className="mb-16">
          <h2 className="text-3xl font-bold text-white text-center mb-8" data-aos="fade-up">
            مميزات الموقع
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {[
              {
                icon: '🌙',
                title: 'تصميم ليلي',
                description: 'واجهة مريحة للعين في الإضاءة المنخفضة'
              },
              {
                icon: '📱',
                title: 'متجاوب',
                description: 'يعمل بسلاسة على جميع الأجهزة'
              },
              {
                icon: '💾',
                title: 'حفظ التقدم',
                description: 'يحفظ تقدمك في التسبيح والقراءة'
              },
              {
                icon: '🎨',
                title: 'تصميم جميل',
                description: 'واجهة زجاجية أنيقة وحديثة'
              }
            ].map((feature, index) => (
              <div
                key={feature.title}
                className="glass-dark rounded-xl p-6 text-center card-hover"
                data-aos="zoom-in"
                data-aos-delay={index * 100}
              >
                <div className="text-4xl mb-4">{feature.icon}</div>
                <h3 className="text-lg font-bold text-white mb-2">{feature.title}</h3>
                <p className="text-gray-300 text-sm">{feature.description}</p>
              </div>
            ))}
          </div>
        </section>

        {/* Call to Action */}
        <section className="text-center" data-aos="fade-up">
          <div className="glass-dark rounded-2xl p-8">
            <h2 className="text-3xl font-bold text-white mb-4">
              ابدأ رحلتك الروحانية اليوم
            </h2>
            <p className="text-gray-300 mb-6">
              انضم إلى آلاف المسلمين الذين يستخدمون موقعنا للتقرب من الله
            </p>
            <Link
              to="/services"
              className="bg-gradient-to-r from-blue-500 to-purple-600 text-white px-8 py-3 rounded-lg font-bold hover:shadow-lg transition-all duration-300 btn-hover inline-block"
            >
              استكشف جميع الخدمات
            </Link>
          </div>
        </section>

      </div>
    </div>
  );
};

export default Home;
