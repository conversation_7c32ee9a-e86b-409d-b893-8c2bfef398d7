import { useState, useEffect } from 'react';
import { AzkarCard } from '../components/Card';
import AOS from 'aos';

const Azkar = () => {
  const [azkarData, setAzkarData] = useState(null);
  const [selectedCategory, setSelectedCategory] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    AOS.init({
      duration: 1000,
      once: true,
    });

    // Load Azkar data
    fetch('/data/azkar.json')
      .then(response => response.json())
      .then(data => {
        setAzkarData(data);
        setLoading(false);
      })
      .catch(error => {
        console.error('Error loading Azkar data:', error);
        setLoading(false);
      });
  }, []);

  const handleCategoryClick = (category) => {
    setSelectedCategory(category);
  };

  const handleBackToCategories = () => {
    setSelectedCategory(null);
  };

  if (loading) {
    return (
      <div className="min-h-screen pt-20 pb-10 flex items-center justify-center">
        <div className="glass-dark rounded-xl p-8 text-center">
          <div className="loading w-16 h-16 border-4 border-purple-500 border-t-transparent rounded-full mx-auto mb-4"></div>
          <p className="text-white">جاري تحميل الأذكار...</p>
        </div>
      </div>
    );
  }

  if (selectedCategory) {
    return (
      <div className="min-h-screen pt-20 pb-10">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Category Header */}
          <div className="glass-dark rounded-xl p-6 mb-8" data-aos="fade-up">
            <div className="flex items-center justify-between mb-4">
              <button
                onClick={handleBackToCategories}
                className="bg-purple-500 hover:bg-purple-600 text-white px-4 py-2 rounded-lg transition-colors duration-300 flex items-center space-x-2 rtl:space-x-reverse"
              >
                <span>←</span>
                <span>العودة للفئات</span>
              </button>
              <div className="text-center">
                <h1 className="text-3xl font-bold text-white arabic-text">{selectedCategory.name}</h1>
                <p className="text-gray-300">{selectedCategory.description}</p>
                <p className="text-purple-400 text-sm">{selectedCategory.time}</p>
              </div>
              <div className="w-20"></div>
            </div>
          </div>

          {/* Azkar List */}
          <div className="space-y-6">
            {selectedCategory.azkar.map((zikr, index) => (
              <div
                key={zikr.id}
                className="glass-dark rounded-xl p-6"
                data-aos="fade-up"
                data-aos-delay={index * 100}
              >
                <div className="mb-4">
                  <div className="flex items-center justify-between mb-4">
                    <div className="w-10 h-10 bg-gradient-to-br from-purple-500 to-pink-600 rounded-full flex items-center justify-center text-white font-bold text-sm">
                      {zikr.id}
                    </div>
                    <div className="text-purple-400 text-sm">
                      يُقال {zikr.count} {zikr.count === 1 ? 'مرة' : 'مرات'}
                    </div>
                  </div>
                  
                  <div className="text-center mb-6">
                    <p className="text-white text-2xl leading-relaxed arabic-text mb-4">
                      {zikr.text}
                    </p>
                    <p className="text-gray-300 text-lg leading-relaxed mb-4">
                      {zikr.translation}
                    </p>
                  </div>

                  {/* Reward */}
                  {zikr.reward && (
                    <div className="bg-yellow-500/10 border border-yellow-500/20 rounded-lg p-4">
                      <h4 className="text-yellow-400 font-bold text-sm mb-2">الثواب:</h4>
                      <p className="text-gray-300 text-sm arabic-text leading-relaxed">
                        {zikr.reward}
                      </p>
                    </div>
                  )}
                </div>

                {/* Counter for this specific zikr */}
                <div className="mt-6 pt-4 border-t border-white/10">
                  <ZikrCounter zikr={zikr} />
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen pt-20 pb-10">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        
        {/* Header */}
        <section className="text-center mb-16" data-aos="fade-up">
          <h1 className="text-4xl md:text-5xl font-bold text-white mb-6">
            الأذكار
          </h1>
          <p className="text-xl text-gray-300 mb-8">
            "وَاذْكُرُوا اللَّهَ كَثِيرًا لَّعَلَّكُمْ تُفْلِحُونَ"
          </p>
          <div className="w-24 h-1 bg-gradient-to-r from-purple-500 to-pink-600 mx-auto rounded-full"></div>
        </section>

        {/* Categories */}
        <section>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {azkarData?.categories.map((category, index) => (
              <div
                key={category.id}
                data-aos="fade-up"
                data-aos-delay={index * 150}
              >
                <AzkarCard
                  category={category}
                  onClick={() => handleCategoryClick(category)}
                />
              </div>
            ))}
          </div>
        </section>

        {/* Benefits Section */}
        <section className="mt-16" data-aos="fade-up">
          <div className="glass-dark rounded-xl p-8">
            <h2 className="text-2xl font-bold text-white text-center mb-6">
              فضل الذكر
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="text-center">
                <div className="w-16 h-16 bg-gradient-to-br from-purple-500 to-pink-600 rounded-full flex items-center justify-center text-2xl mx-auto mb-4">
                  💎
                </div>
                <h3 className="text-lg font-bold text-white mb-2">طمأنينة القلب</h3>
                <p className="text-gray-300 text-sm">
                  "أَلَا بِذِكْرِ اللَّهِ تَطْمَئِنُّ الْقُلُوبُ"
                </p>
              </div>
              <div className="text-center">
                <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-2xl mx-auto mb-4">
                  🌟
                </div>
                <h3 className="text-lg font-bold text-white mb-2">محو الذنوب</h3>
                <p className="text-gray-300 text-sm">
                  الذكر يمحو الذنوب ويرفع الدرجات
                </p>
              </div>
            </div>
          </div>
        </section>

      </div>
    </div>
  );
};

// Simple counter component for individual zikr
const ZikrCounter = ({ zikr }) => {
  const [count, setCount] = useState(0);
  const target = zikr.count;
  const progress = Math.min((count / target) * 100, 100);
  const isCompleted = count >= target;

  const increment = () => {
    setCount(prev => prev + 1);
    if (navigator.vibrate) {
      navigator.vibrate(50);
    }
  };

  const reset = () => {
    setCount(0);
  };

  return (
    <div className="text-center">
      <div className="flex items-center justify-center space-x-4 rtl:space-x-reverse mb-4">
        <div className="text-center">
          <div className={`text-2xl font-bold ${isCompleted ? 'text-green-400' : 'text-white'}`}>
            {count}
          </div>
          <div className="text-gray-400 text-sm">من {target}</div>
        </div>
        <div className="w-20 h-20">
          <svg className="w-full h-full transform -rotate-90" viewBox="0 0 100 100">
            <circle
              cx="50"
              cy="50"
              r="40"
              stroke="rgba(255,255,255,0.1)"
              strokeWidth="8"
              fill="none"
            />
            <circle
              cx="50"
              cy="50"
              r="40"
              stroke={isCompleted ? "#10B981" : "#8B5CF6"}
              strokeWidth="8"
              fill="none"
              strokeLinecap="round"
              strokeDasharray={`${2 * Math.PI * 40}`}
              strokeDashoffset={`${2 * Math.PI * 40 * (1 - progress / 100)}`}
              className="transition-all duration-300"
            />
          </svg>
        </div>
      </div>
      
      <div className="flex space-x-2 rtl:space-x-reverse justify-center">
        <button
          onClick={increment}
          className={`px-4 py-2 rounded-lg font-medium transition-colors duration-300 ${
            isCompleted
              ? 'bg-green-500 hover:bg-green-600 text-white'
              : 'bg-purple-500 hover:bg-purple-600 text-white'
          }`}
        >
          {isCompleted ? '✅ مكتمل' : '🤲 ذكر'}
        </button>
        <button
          onClick={reset}
          className="px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-lg transition-colors duration-300"
        >
          🔄
        </button>
      </div>
    </div>
  );
};

export default Azkar;
