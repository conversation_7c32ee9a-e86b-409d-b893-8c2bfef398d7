import { useState, useEffect } from 'react';
import { QuranCard } from '../components/Card';
import AOS from 'aos';

const Quran = () => {
  const [quranData, setQuranData] = useState(null);
  const [selectedSurah, setSelectedSurah] = useState(null);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');

  useEffect(() => {
    AOS.init({
      duration: 1000,
      once: true,
    });

    // Load Quran data
    fetch('/data/quran.json')
      .then(response => response.json())
      .then(data => {
        setQuranData(data);
        setLoading(false);
      })
      .catch(error => {
        console.error('Error loading Quran data:', error);
        setLoading(false);
      });
  }, []);

  const filteredSurahs = quranData?.surahs?.filter(surah =>
    surah.name.includes(searchTerm) ||
    surah.englishName.toLowerCase().includes(searchTerm.toLowerCase()) ||
    surah.meaning.toLowerCase().includes(searchTerm.toLowerCase())
  ) || [];

  const handleSurahClick = (surah) => {
    setSelectedSurah(surah);
  };

  const handleBackToList = () => {
    setSelectedSurah(null);
  };

  if (loading) {
    return (
      <div className="min-h-screen pt-20 pb-10 flex items-center justify-center">
        <div className="glass-dark rounded-xl p-8 text-center">
          <div className="loading w-16 h-16 border-4 border-blue-500 border-t-transparent rounded-full mx-auto mb-4"></div>
          <p className="text-white">جاري تحميل القرآن الكريم...</p>
        </div>
      </div>
    );
  }

  if (selectedSurah) {
    return (
      <div className="min-h-screen pt-20 pb-10">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Surah Header */}
          <div className="glass-dark rounded-xl p-6 mb-8" data-aos="fade-up">
            <div className="flex items-center justify-between mb-4">
              <button
                onClick={handleBackToList}
                className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition-colors duration-300 flex items-center space-x-2 rtl:space-x-reverse"
              >
                <span>←</span>
                <span>العودة للقائمة</span>
              </button>
              <div className="text-center">
                <h1 className="text-3xl font-bold text-white arabic-text">{selectedSurah.name}</h1>
                <p className="text-gray-300">{selectedSurah.englishName} - {selectedSurah.meaning}</p>
                <p className="text-blue-400 text-sm">{selectedSurah.type} • {selectedSurah.ayahCount} آية</p>
              </div>
              <div className="w-20"></div>
            </div>
          </div>

          {/* Ayahs */}
          <div className="space-y-6">
            {selectedSurah.ayahs.map((ayah, index) => (
              <div
                key={ayah.id}
                className="glass-dark rounded-xl p-6"
                data-aos="fade-up"
                data-aos-delay={index * 100}
              >
                <div className="flex items-start space-x-4 rtl:space-x-reverse">
                  <div className="w-10 h-10 bg-gradient-to-br from-green-500 to-teal-600 rounded-full flex items-center justify-center text-white font-bold text-sm flex-shrink-0">
                    {ayah.id}
                  </div>
                  <div className="flex-1">
                    <p className="text-white text-xl leading-relaxed arabic-text mb-4">
                      {ayah.text}
                    </p>
                    <p className="text-gray-300 text-sm leading-relaxed">
                      {ayah.translation}
                    </p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen pt-20 pb-10">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        
        {/* Header */}
        <section className="text-center mb-16" data-aos="fade-up">
          <h1 className="text-4xl md:text-5xl font-bold text-white mb-6">
            القرآن الكريم
          </h1>
          <p className="text-xl text-gray-300 mb-8">
            "وَنُنَزِّلُ مِنَ الْقُرْآنِ مَا هُوَ شِفَاءٌ وَرَحْمَةٌ لِّلْمُؤْمِنِينَ"
          </p>
          <div className="w-24 h-1 bg-gradient-to-r from-green-500 to-teal-600 mx-auto rounded-full"></div>
        </section>

        {/* Statistics */}
        {quranData && (
          <section className="mb-16" data-aos="fade-up">
            <div className="glass-dark rounded-xl p-8">
              <h2 className="text-2xl font-bold text-white text-center mb-6">
                إحصائيات القرآن الكريم
              </h2>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
                <div className="text-center">
                  <div className="text-3xl font-bold text-green-400 mb-2">
                    {quranData.info.totalSurahs}
                  </div>
                  <div className="text-gray-300">سورة</div>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold text-green-400 mb-2">
                    {quranData.info.totalAyahs.toLocaleString()}
                  </div>
                  <div className="text-gray-300">آية</div>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold text-green-400 mb-2">
                    {quranData.info.totalJuz}
                  </div>
                  <div className="text-gray-300">جزء</div>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold text-green-400 mb-2">
                    {quranData.info.totalPages}
                  </div>
                  <div className="text-gray-300">صفحة</div>
                </div>
              </div>
            </div>
          </section>
        )}

        {/* Search */}
        <section className="mb-8" data-aos="fade-up">
          <div className="max-w-md mx-auto">
            <div className="relative">
              <input
                type="text"
                placeholder="ابحث في السور..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
              />
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <svg className="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
              </div>
            </div>
          </div>
        </section>

        {/* Surahs List */}
        <section>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredSurahs.map((surah, index) => (
              <div
                key={surah.id}
                data-aos="fade-up"
                data-aos-delay={index * 50}
              >
                <QuranCard
                  surah={surah}
                  onClick={() => handleSurahClick(surah)}
                />
              </div>
            ))}
          </div>

          {filteredSurahs.length === 0 && searchTerm && (
            <div className="text-center py-12" data-aos="fade-up">
              <div className="glass-dark rounded-xl p-8">
                <div className="text-6xl mb-4">🔍</div>
                <h3 className="text-xl font-bold text-white mb-2">لم يتم العثور على نتائج</h3>
                <p className="text-gray-300">جرب البحث بكلمات مختلفة</p>
              </div>
            </div>
          )}
        </section>

      </div>
    </div>
  );
};

export default Quran;
