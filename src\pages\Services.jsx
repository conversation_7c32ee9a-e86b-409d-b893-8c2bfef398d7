import { useEffect } from 'react';
import { ServiceCard } from '../components/Card';
import AOS from 'aos';

const Services = () => {
  useEffect(() => {
    AOS.init({
      duration: 1000,
      once: true,
    });
  }, []);

  const services = [
    {
      name: 'القرآن الكريم',
      description: 'اقرأ وتدبر آيات القرآن الكريم مع الترجمة والتفسير',
      icon: '📖',
      link: '/quran',
      gradient: 'from-green-500 to-teal-600',
      features: [
        'جميع سور القرآن الكريم',
        'ترجمة معاني الآيات',
        'تصفح سهل ومريح',
        'البحث في الآيات'
      ]
    },
    {
      name: 'الأذكار',
      description: 'مجموعة شاملة من الأذكار المصنفة حسب الأوقات والمناسبات',
      icon: '🤲',
      link: '/azkar',
      gradient: 'from-purple-500 to-pink-600',
      features: [
        'أذكار الصباح والمساء',
        'أذكار النوم والاستيقاظ',
        'أذكار الصلاة',
        'أذكار متنوعة'
      ]
    },
    {
      name: 'التسبيح',
      description: 'مسبحة إلكترونية تفاعلية مع عداد وحفظ التقدم',
      icon: '📿',
      link: '/tasbeeh',
      gradient: 'from-blue-500 to-indigo-600',
      features: [
        'عداد تفاعلي',
        'حفظ التقدم',
        'أهداف قابلة للتخصيص',
        'تسبيحات متنوعة'
      ]
    },
    {
      name: 'أسماء الله الحسنى',
      description: 'تعرف على أسماء الله الحسنى ومعانيها',
      icon: '✨',
      link: '/names',
      gradient: 'from-yellow-500 to-orange-600',
      features: [
        '99 اسماً من أسماء الله',
        'معاني الأسماء',
        'فضائل الأسماء',
        'أدعية مرتبطة'
      ]
    },
    {
      name: 'الأدعية',
      description: 'مجموعة من الأدعية المأثورة من القرآن والسنة',
      icon: '🤲',
      link: '/duas',
      gradient: 'from-pink-500 to-rose-600',
      features: [
        'أدعية من القرآن',
        'أدعية من السنة',
        'أدعية للمناسبات',
        'أدعية يومية'
      ]
    },
    {
      name: 'القبلة',
      description: 'تحديد اتجاه القبلة بدقة باستخدام الموقع الجغرافي',
      icon: '🧭',
      link: '/qibla',
      gradient: 'from-teal-500 to-cyan-600',
      features: [
        'تحديد دقيق للقبلة',
        'استخدام GPS',
        'بوصلة تفاعلية',
        'معلومات المسافة'
      ]
    }
  ];

  return (
    <div className="min-h-screen pt-20 pb-10">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        
        {/* Header */}
        <section className="text-center mb-16" data-aos="fade-up">
          <h1 className="text-4xl md:text-5xl font-bold text-white mb-6">
            خدماتنا الإسلامية
          </h1>
          <p className="text-xl text-gray-300 mb-8 max-w-3xl mx-auto">
            مجموعة شاملة من الخدمات الإسلامية المصممة لمساعدتك في رحلتك الروحانية
            والتقرب من الله عز وجل
          </p>
          <div className="w-24 h-1 bg-gradient-to-r from-blue-500 to-purple-600 mx-auto rounded-full"></div>
        </section>

        {/* Services Grid */}
        <section className="mb-16">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {services.map((service, index) => (
              <div
                key={service.name}
                data-aos="fade-up"
                data-aos-delay={index * 150}
                className="group"
              >
                <div className="glass-dark rounded-xl p-6 card-hover h-full">
                  {/* Service Header */}
                  <div className="flex items-center space-x-3 rtl:space-x-reverse mb-4">
                    <div className={`w-12 h-12 bg-gradient-to-br ${service.gradient} rounded-lg flex items-center justify-center text-2xl`}>
                      {service.icon}
                    </div>
                    <div>
                      <h3 className="text-xl font-bold text-white">{service.name}</h3>
                    </div>
                  </div>

                  {/* Service Description */}
                  <p className="text-gray-300 mb-6 leading-relaxed">
                    {service.description}
                  </p>

                  {/* Service Features */}
                  <div className="mb-6">
                    <h4 className="text-white font-semibold mb-3">المميزات:</h4>
                    <ul className="space-y-2">
                      {service.features.map((feature, featureIndex) => (
                        <li key={featureIndex} className="flex items-center space-x-2 rtl:space-x-reverse text-gray-300">
                          <span className="text-green-400">✓</span>
                          <span className="text-sm">{feature}</span>
                        </li>
                      ))}
                    </ul>
                  </div>

                  {/* Action Button */}
                  <div className="mt-auto">
                    <a
                      href={service.link}
                      className={`block w-full text-center py-3 px-4 bg-gradient-to-r ${service.gradient} text-white rounded-lg font-medium hover:shadow-lg transition-all duration-300 btn-hover`}
                    >
                      استكشف الخدمة
                    </a>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </section>

        {/* Additional Information */}
        <section className="mb-16" data-aos="fade-up">
          <div className="glass-dark rounded-xl p-8">
            <h2 className="text-2xl font-bold text-white mb-6 text-center">
              لماذا تختار خدماتنا؟
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="text-center">
                <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-2xl mx-auto mb-4">
                  🎯
                </div>
                <h3 className="text-lg font-bold text-white mb-2">دقة وموثوقية</h3>
                <p className="text-gray-300 text-sm">
                  جميع المحتويات مراجعة ومدققة من مصادر موثوقة
                </p>
              </div>
              <div className="text-center">
                <div className="w-16 h-16 bg-gradient-to-br from-green-500 to-teal-600 rounded-full flex items-center justify-center text-2xl mx-auto mb-4">
                  📱
                </div>
                <h3 className="text-lg font-bold text-white mb-2">سهولة الاستخدام</h3>
                <p className="text-gray-300 text-sm">
                  واجهة بسيطة ومريحة تعمل على جميع الأجهزة
                </p>
              </div>
              <div className="text-center">
                <div className="w-16 h-16 bg-gradient-to-br from-purple-500 to-pink-600 rounded-full flex items-center justify-center text-2xl mx-auto mb-4">
                  🔄
                </div>
                <h3 className="text-lg font-bold text-white mb-2">تحديث مستمر</h3>
                <p className="text-gray-300 text-sm">
                  نضيف محتوى جديد ونحسن الخدمات باستمرار
                </p>
              </div>
            </div>
          </div>
        </section>

        {/* Call to Action */}
        <section className="text-center" data-aos="fade-up">
          <div className="glass-dark rounded-xl p-8">
            <h2 className="text-2xl font-bold text-white mb-4">
              هل تحتاج مساعدة؟
            </h2>
            <p className="text-gray-300 mb-6">
              إذا كان لديك أي استفسار أو اقتراح، لا تتردد في التواصل معنا
            </p>
            <a
              href="/about"
              className="bg-gradient-to-r from-blue-500 to-purple-600 text-white px-8 py-3 rounded-lg font-bold hover:shadow-lg transition-all duration-300 btn-hover inline-block"
            >
              تواصل معنا
            </a>
          </div>
        </section>

      </div>
    </div>
  );
};

export default Services;
