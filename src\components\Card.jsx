import { Link } from 'react-router-dom';

const Card = ({ 
  title, 
  description, 
  icon, 
  link, 
  stats, 
  gradient = "from-blue-500 to-purple-600",
  onClick,
  className = "",
  children 
}) => {
  const CardContent = () => (
    <div className={`glass-dark rounded-xl p-6 card-hover transition-all duration-300 ${className}`}>
      {/* Icon and Title */}
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center space-x-3 rtl:space-x-reverse">
          <div className={`w-12 h-12 bg-gradient-to-br ${gradient} rounded-lg flex items-center justify-center text-2xl`}>
            {icon}
          </div>
          <div>
            <h3 className="text-xl font-bold text-white">{title}</h3>
            {description && (
              <p className="text-gray-300 text-sm mt-1">{description}</p>
            )}
          </div>
        </div>
        
        {/* Arrow Icon */}
        <div className="text-gray-400 group-hover:text-white transition-colors duration-300">
          <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
          </svg>
        </div>
      </div>

      {/* Stats */}
      {stats && (
        <div className="grid grid-cols-2 gap-4 mb-4">
          {stats.map((stat, index) => (
            <div key={index} className="text-center p-3 bg-white/5 rounded-lg">
              <div className="text-2xl font-bold text-blue-400">{stat.value}</div>
              <div className="text-sm text-gray-300">{stat.label}</div>
            </div>
          ))}
        </div>
      )}

      {/* Custom Content */}
      {children}

      {/* Action Button */}
      <div className="mt-4 pt-4 border-t border-white/10">
        <div className="flex items-center justify-center space-x-2 rtl:space-x-reverse text-blue-400 hover:text-blue-300 transition-colors duration-300">
          <span className="text-sm font-medium">استكشف المزيد</span>
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
          </svg>
        </div>
      </div>
    </div>
  );

  if (onClick) {
    return (
      <div onClick={onClick} className="cursor-pointer group">
        <CardContent />
      </div>
    );
  }

  if (link) {
    return (
      <Link to={link} className="block group">
        <CardContent />
      </Link>
    );
  }

  return <CardContent />;
};

// Specialized Card Components
export const ServiceCard = ({ service }) => (
  <Card
    title={service.name}
    description={service.description}
    icon={service.icon}
    link={service.link}
    gradient={service.gradient}
  />
);

export const StatsCard = ({ title, stats, icon, gradient }) => (
  <Card
    title={title}
    icon={icon}
    stats={stats}
    gradient={gradient}
  />
);

export const QuranCard = ({ surah, onClick }) => (
  <Card
    title={surah.name}
    description={`${surah.englishName} - ${surah.meaning}`}
    icon="📖"
    onClick={onClick}
    gradient="from-green-500 to-teal-600"
    className="text-right"
  >
    <div className="mt-4 space-y-2">
      <div className="flex justify-between items-center text-sm">
        <span className="text-gray-300">النوع:</span>
        <span className="text-blue-400">{surah.type}</span>
      </div>
      <div className="flex justify-between items-center text-sm">
        <span className="text-gray-300">عدد الآيات:</span>
        <span className="text-blue-400">{surah.ayahCount}</span>
      </div>
    </div>
  </Card>
);

export const AzkarCard = ({ category, onClick }) => (
  <Card
    title={category.name}
    description={category.description}
    icon="🤲"
    onClick={onClick}
    gradient="from-purple-500 to-pink-600"
    className="text-right"
  >
    <div className="mt-4 space-y-2">
      <div className="flex justify-between items-center text-sm">
        <span className="text-gray-300">الوقت:</span>
        <span className="text-blue-400">{category.time}</span>
      </div>
      <div className="flex justify-between items-center text-sm">
        <span className="text-gray-300">عدد الأذكار:</span>
        <span className="text-blue-400">{category.azkar?.length || 0}</span>
      </div>
    </div>
  </Card>
);

export default Card;
