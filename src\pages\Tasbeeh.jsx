import { useState, useEffect } from 'react';
import TasbeehCounter from '../components/TasbeehCounter';
import AOS from 'aos';

const Tasbeeh = () => {
  const [tasbeehData, setTasbeehData] = useState(null);
  const [selectedTasbeeh, setSelectedTasbeeh] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    AOS.init({
      duration: 1000,
      once: true,
    });

    // Load Tasbeeh data
    fetch('/data/tasbeeh.json')
      .then(response => response.json())
      .then(data => {
        setTasbeehData(data);
        // Set default tasbeeh to first one
        if (data.tasbeeh && data.tasbeeh.length > 0) {
          setSelectedTasbeeh(data.tasbeeh[0]);
        }
        setLoading(false);
      })
      .catch(error => {
        console.error('Error loading Tasbeeh data:', error);
        setLoading(false);
      });
  }, []);

  const handleTasbeehSelect = (tasbeeh) => {
    setSelectedTasbeeh(tasbeeh);
  };

  const handleComplete = (count, target) => {
    // Show completion notification
    if (navigator.vibrate) {
      navigator.vibrate([200, 100, 200, 100, 200]);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen pt-20 pb-10 flex items-center justify-center">
        <div className="glass-dark rounded-xl p-8 text-center">
          <div className="loading w-16 h-16 border-4 border-blue-500 border-t-transparent rounded-full mx-auto mb-4"></div>
          <p className="text-white">جاري تحميل المسبحة...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen pt-20 pb-10">
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
        
        {/* Header */}
        <section className="text-center mb-16" data-aos="fade-up">
          <h1 className="text-4xl md:text-5xl font-bold text-white mb-6">
            المسبحة الإلكترونية
          </h1>
          <p className="text-xl text-gray-300 mb-8">
            "فَاذْكُرُونِي أَذْكُرْكُمْ وَاشْكُرُوا لِي وَلَا تَكْفُرُونِ"
          </p>
          <div className="w-24 h-1 bg-gradient-to-r from-blue-500 to-indigo-600 mx-auto rounded-full"></div>
        </section>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          
          {/* Tasbeeh Selection */}
          <div className="lg:col-span-1">
            <div className="glass-dark rounded-xl p-6" data-aos="fade-right">
              <h2 className="text-xl font-bold text-white mb-6 text-center">
                اختر التسبيح
              </h2>
              <div className="space-y-3">
                {tasbeehData?.tasbeeh.map((tasbeeh, index) => (
                  <button
                    key={tasbeeh.id}
                    onClick={() => handleTasbeehSelect(tasbeeh)}
                    className={`w-full p-4 rounded-lg text-right transition-all duration-300 ${
                      selectedTasbeeh?.id === tasbeeh.id
                        ? 'bg-blue-500 text-white'
                        : 'bg-white/5 hover:bg-white/10 text-gray-300'
                    }`}
                  >
                    <div className="font-bold arabic-text text-lg mb-1">
                      {tasbeeh.text}
                    </div>
                    <div className="text-sm opacity-75">
                      {tasbeeh.englishText}
                    </div>
                    <div className="text-xs mt-2 opacity-60">
                      العدد المستحب: {tasbeeh.recommendedCount}
                    </div>
                  </button>
                ))}
              </div>

              {/* Quick Actions */}
              <div className="mt-6 pt-6 border-t border-white/10">
                <h3 className="text-lg font-bold text-white mb-4 text-center">
                  تسبيحات سريعة
                </h3>
                <div className="grid grid-cols-3 gap-2">
                  {[
                    { text: 'سُبْحَانَ اللَّهِ', count: 33 },
                    { text: 'الْحَمْدُ لِلَّهِ', count: 33 },
                    { text: 'اللَّهُ أَكْبَرُ', count: 34 }
                  ].map((quick, index) => (
                    <button
                      key={index}
                      onClick={() => {
                        const quickTasbeeh = tasbeehData.tasbeeh.find(t => t.text === quick.text);
                        if (quickTasbeeh) handleTasbeehSelect(quickTasbeeh);
                      }}
                      className="p-3 bg-gradient-to-br from-green-500 to-teal-600 text-white rounded-lg text-sm font-bold hover:shadow-lg transition-all duration-300"
                    >
                      <div className="arabic-text">{quick.text}</div>
                      <div className="text-xs opacity-75">{quick.count}</div>
                    </button>
                  ))}
                </div>
              </div>
            </div>
          </div>

          {/* Main Counter */}
          <div className="lg:col-span-2">
            <div data-aos="fade-left">
              {selectedTasbeeh ? (
                <TasbeehCounter
                  tasbeeh={selectedTasbeeh}
                  onComplete={handleComplete}
                />
              ) : (
                <div className="glass-dark rounded-xl p-8 text-center">
                  <div className="text-6xl mb-4">📿</div>
                  <h3 className="text-xl font-bold text-white mb-2">اختر تسبيحة للبدء</h3>
                  <p className="text-gray-300">اختر من القائمة على اليسار لتبدأ التسبيح</p>
                </div>
              )}
            </div>
          </div>

        </div>

        {/* Benefits Section */}
        <section className="mt-16" data-aos="fade-up">
          <div className="glass-dark rounded-xl p-8">
            <h2 className="text-2xl font-bold text-white text-center mb-8">
              فضائل التسبيح
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="text-center">
                <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-full flex items-center justify-center text-2xl mx-auto mb-4">
                  ⚖️
                </div>
                <h3 className="text-lg font-bold text-white mb-2">ثقيل في الميزان</h3>
                <p className="text-gray-300 text-sm">
                  "سُبْحَانَ اللَّهِ وَبِحَمْدِهِ" ثقيلتان في الميزان
                </p>
              </div>
              <div className="text-center">
                <div className="w-16 h-16 bg-gradient-to-br from-green-500 to-teal-600 rounded-full flex items-center justify-center text-2xl mx-auto mb-4">
                  🌳
                </div>
                <h3 className="text-lg font-bold text-white mb-2">غراس الجنة</h3>
                <p className="text-gray-300 text-sm">
                  التسبيح والتحميد والتكبير غراس الجنة
                </p>
              </div>
              <div className="text-center">
                <div className="w-16 h-16 bg-gradient-to-br from-purple-500 to-pink-600 rounded-full flex items-center justify-center text-2xl mx-auto mb-4">
                  💎
                </div>
                <h3 className="text-lg font-bold text-white mb-2">أحب الكلام</h3>
                <p className="text-gray-300 text-sm">
                  أحب الكلام إلى الله أربع: سبحان الله والحمد لله ولا إله إلا الله والله أكبر
                </p>
              </div>
            </div>
          </div>
        </section>

        {/* Instructions */}
        <section className="mt-16" data-aos="fade-up">
          <div className="glass-dark rounded-xl p-8">
            <h2 className="text-2xl font-bold text-white text-center mb-6">
              كيفية الاستخدام
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              <div>
                <h3 className="text-lg font-bold text-white mb-4">الخطوات:</h3>
                <ol className="space-y-3 text-gray-300">
                  <li className="flex items-start space-x-3 rtl:space-x-reverse">
                    <span className="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center text-white text-sm font-bold flex-shrink-0">1</span>
                    <span>اختر التسبيحة المرغوبة من القائمة</span>
                  </li>
                  <li className="flex items-start space-x-3 rtl:space-x-reverse">
                    <span className="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center text-white text-sm font-bold flex-shrink-0">2</span>
                    <span>اضغط على زر التسبيح لبدء العد</span>
                  </li>
                  <li className="flex items-start space-x-3 rtl:space-x-reverse">
                    <span className="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center text-white text-sm font-bold flex-shrink-0">3</span>
                    <span>يمكنك تغيير الهدف حسب رغبتك</span>
                  </li>
                  <li className="flex items-start space-x-3 rtl:space-x-reverse">
                    <span className="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center text-white text-sm font-bold flex-shrink-0">4</span>
                    <span>سيتم حفظ تقدمك تلقائياً</span>
                  </li>
                </ol>
              </div>
              <div>
                <h3 className="text-lg font-bold text-white mb-4">المميزات:</h3>
                <ul className="space-y-3 text-gray-300">
                  <li className="flex items-center space-x-3 rtl:space-x-reverse">
                    <span className="text-green-400">✓</span>
                    <span>حفظ التقدم تلقائياً</span>
                  </li>
                  <li className="flex items-center space-x-3 rtl:space-x-reverse">
                    <span className="text-green-400">✓</span>
                    <span>اهتزاز عند الإكمال (للهواتف)</span>
                  </li>
                  <li className="flex items-center space-x-3 rtl:space-x-reverse">
                    <span className="text-green-400">✓</span>
                    <span>أهداف قابلة للتخصيص</span>
                  </li>
                  <li className="flex items-center space-x-3 rtl:space-x-reverse">
                    <span className="text-green-400">✓</span>
                    <span>واجهة سهلة الاستخدام</span>
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </section>

      </div>
    </div>
  );
};

export default Tasbeeh;
