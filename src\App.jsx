import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { useEffect } from 'react';
import AOS from 'aos';

// Components
import Navbar from './components/Navbar';
import Footer from './components/Footer';

// Pages
import Home from './pages/Home';
import Services from './pages/Services';
import About from './pages/About';
import Quran from './pages/Quran';
import <PERSON>zkar from './pages/Azkar';
import Tasbeeh from './pages/Tasbeeh';

function App() {
  useEffect(() => {
    // Initialize AOS
    AOS.init({
      duration: 1000,
      once: true,
      easing: 'ease-out-cubic',
    });

    // Set document direction to RTL
    document.documentElement.dir = 'rtl';
    document.documentElement.lang = 'ar';

    // Set page title
    document.title = 'الذكر الحكيم - موقع إسلامي شامل';

    // Add meta description
    const metaDescription = document.querySelector('meta[name="description"]');
    if (metaDescription) {
      metaDescription.setAttribute('content', 'موقع إسلامي شامل يحتوي على القرآن الكريم والأذكار والتسبيح لتقريب المسلم من ربه');
    }

    // Add viewport meta for mobile responsiveness
    const viewport = document.querySelector('meta[name="viewport"]');
    if (viewport) {
      viewport.setAttribute('content', 'width=device-width, initial-scale=1.0, user-scalable=yes');
    }
  }, []);

  return (
    <Router>
      <div className="App min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 islamic-pattern">
        <Navbar />
        
        <main className="relative z-10">
          <Routes>
            <Route path="/" element={<Home />} />
            <Route path="/services" element={<Services />} />
            <Route path="/about" element={<About />} />
            <Route path="/quran" element={<Quran />} />
            <Route path="/azkar" element={<Azkar />} />
            <Route path="/tasbeeh" element={<Tasbeeh />} />
            
            {/* 404 Page */}
            <Route path="*" element={<NotFound />} />
          </Routes>
        </main>

        <Footer />
      </div>
    </Router>
  );
}

// 404 Not Found Component
const NotFound = () => {
  return (
    <div className="min-h-screen pt-20 pb-10 flex items-center justify-center">
      <div className="max-w-md mx-auto px-4 text-center">
        <div className="glass-dark rounded-xl p-8">
          <div className="text-6xl mb-6">🔍</div>
          <h1 className="text-3xl font-bold text-white mb-4">
            الصفحة غير موجودة
          </h1>
          <p className="text-gray-300 mb-6">
            عذراً، الصفحة التي تبحث عنها غير موجودة أو تم نقلها.
          </p>
          <div className="space-y-4">
            <a
              href="/"
              className="block w-full bg-gradient-to-r from-blue-500 to-purple-600 text-white py-3 px-6 rounded-lg font-bold hover:shadow-lg transition-all duration-300 btn-hover"
            >
              🏠 العودة للرئيسية
            </a>
            <a
              href="/services"
              className="block w-full bg-gradient-to-r from-green-500 to-teal-600 text-white py-3 px-6 rounded-lg font-bold hover:shadow-lg transition-all duration-300 btn-hover"
            >
              ⚙️ تصفح الخدمات
            </a>
          </div>
        </div>
      </div>
    </div>
  );
};

export default App;
