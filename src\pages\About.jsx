import { useEffect } from 'react';
import AOS from 'aos';

const About = () => {
  useEffect(() => {
    AOS.init({
      duration: 1000,
      once: true,
    });
  }, []);

  return (
    <div className="min-h-screen pt-20 pb-10">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        
        {/* Header */}
        <section className="text-center mb-16" data-aos="fade-up">
          <h1 className="text-4xl md:text-5xl font-bold text-white mb-6">
            حول الذكر الحكيم
          </h1>
          <p className="text-xl text-gray-300 mb-8">
            موقع إسلامي شامل يهدف إلى تقريب المسلم من ربه
          </p>
          <div className="w-24 h-1 bg-gradient-to-r from-blue-500 to-purple-600 mx-auto rounded-full"></div>
        </section>

        {/* Mission */}
        <section className="mb-16" data-aos="fade-up">
          <div className="glass-dark rounded-xl p-8">
            <h2 className="text-2xl font-bold text-white mb-6 text-center">
              رسالتنا
            </h2>
            <p className="text-gray-300 leading-relaxed text-lg text-center arabic-text">
              نسعى إلى توفير منصة إسلامية شاملة وسهلة الاستخدام تساعد المسلمين في جميع أنحاء العالم
              على التقرب من الله عز وجل من خلال قراءة القرآن الكريم وذكر الله والتسبيح.
              نؤمن بأن التكنولوجيا يمكن أن تكون وسيلة فعالة لنشر الخير وتسهيل العبادة.
            </p>
          </div>
        </section>

        {/* Vision */}
        <section className="mb-16" data-aos="fade-up">
          <div className="glass-dark rounded-xl p-8">
            <h2 className="text-2xl font-bold text-white mb-6 text-center">
              رؤيتنا
            </h2>
            <p className="text-gray-300 leading-relaxed text-lg text-center arabic-text">
              أن نكون المرجع الأول للمسلمين في العالم الرقمي للوصول إلى المحتوى الإسلامي الأصيل
              والموثوق، وأن نساهم في إحياء سنة الذكر والتسبيح في حياة المسلمين اليومية.
            </p>
          </div>
        </section>

        {/* Features */}
        <section className="mb-16" data-aos="fade-up">
          <h2 className="text-2xl font-bold text-white mb-8 text-center">
            ما يميزنا
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {[
              {
                icon: '📖',
                title: 'محتوى أصيل',
                description: 'جميع النصوص مأخوذة من مصادر موثوقة ومراجعة بعناية'
              },
              {
                icon: '🎨',
                title: 'تصميم جميل',
                description: 'واجهة مستخدم أنيقة ومريحة للعين مع تصميم زجاجي حديث'
              },
              {
                icon: '📱',
                title: 'متجاوب',
                description: 'يعمل بسلاسة على جميع الأجهزة من الهاتف إلى الكمبيوتر'
              },
              {
                icon: '💾',
                title: 'حفظ التقدم',
                description: 'يحفظ تقدمك في القراءة والتسبيح لتتابع من حيث توقفت'
              },
              {
                icon: '🌙',
                title: 'وضع ليلي',
                description: 'تصميم مريح للعين في الإضاءة المنخفضة'
              },
              {
                icon: '🔍',
                title: 'بحث متقدم',
                description: 'إمكانية البحث في القرآن والأذكار بسهولة'
              }
            ].map((feature, index) => (
              <div
                key={feature.title}
                className="glass-dark rounded-xl p-6"
                data-aos="fade-up"
                data-aos-delay={index * 100}
              >
                <div className="flex items-start space-x-4 rtl:space-x-reverse">
                  <div className="text-3xl">{feature.icon}</div>
                  <div>
                    <h3 className="text-lg font-bold text-white mb-2">{feature.title}</h3>
                    <p className="text-gray-300 text-sm">{feature.description}</p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </section>

        {/* Developer Info */}
        <section className="mb-16" data-aos="fade-up">
          <div className="glass-dark rounded-xl p-8 text-center">
            <h2 className="text-2xl font-bold text-white mb-6">
              عن المطور
            </h2>
            <div className="max-w-2xl mx-auto">
              <p className="text-gray-300 leading-relaxed mb-6">
                تم تطوير هذا الموقع بحب وإخلاص لوجه الله تعالى، بهدف خدمة الإسلام والمسلمين
                وتسهيل الوصول إلى المحتوى الإسلامي الأصيل. نسأل الله أن يتقبل منا هذا العمل
                وأن يجعله في ميزان حسناتنا.
              </p>
              <div className="flex justify-center space-x-4 rtl:space-x-reverse">
                <a
                  href="mailto:<EMAIL>"
                  className="bg-blue-500 hover:bg-blue-600 text-white px-6 py-2 rounded-lg transition-colors duration-300"
                >
                  📧 راسلنا
                </a>
                <a
                  href="#"
                  className="bg-green-500 hover:bg-green-600 text-white px-6 py-2 rounded-lg transition-colors duration-300"
                >
                  💬 واتساب
                </a>
              </div>
            </div>
          </div>
        </section>

        {/* Disclaimer */}
        <section className="mb-16" data-aos="fade-up">
          <div className="glass-dark rounded-xl p-8">
            <h2 className="text-2xl font-bold text-white mb-6 text-center">
              إخلاء مسؤولية
            </h2>
            <div className="text-gray-300 leading-relaxed space-y-4">
              <p>
                • جميع النصوص القرآنية والأحاديث والأذكار الواردة في هذا الموقع مأخوذة من مصادر موثوقة
              </p>
              <p>
                • نحن نبذل قصارى جهدنا لضمان دقة المحتوى، ولكن ننصح بالرجوع إلى المصادر الأصلية للتأكد
              </p>
              <p>
                • هذا الموقع مجاني تماماً ولا نهدف من ورائه إلى أي ربح مادي
              </p>
              <p>
                • نرحب بأي اقتراحات أو تصحيحات لتحسين المحتوى
              </p>
            </div>
          </div>
        </section>

        {/* Quranic Verse */}
        <section className="text-center" data-aos="fade-up">
          <div className="glass-dark rounded-xl p-8">
            <p className="text-gold-400 font-arabic text-2xl mb-4">
              "وَمَنْ أَحْسَنُ قَوْلًا مِّمَّن دَعَا إِلَى اللَّهِ وَعَمِلَ صَالِحًا وَقَالَ إِنَّنِي مِنَ الْمُسْلِمِينَ"
            </p>
            <p className="text-gray-300">
              سورة فصلت - آية 33
            </p>
          </div>
        </section>

      </div>
    </div>
  );
};

export default About;
